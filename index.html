<!DOCTYPE html>
<html class="no-js" lang="es">
  <head>
    <!-- End Wayback Rewrite JS Include -->

    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>About us &raquo; CAV Investment Group</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link
      rel="preconnect"
      href="https://web.archive.org/web/20250418055804/https://fonts.gstatic.com/"
      crossorigin
    />
    <link
      rel="preload"
      as="style"
      href="https://web.archive.org/web/20250418055804/https://fonts.googleapis.com/css2?family=Commissioner:wght@400;500&amp;family=Playfair+Display:wght@400;600&amp;display=swap"
    />

    <link
      rel="stylesheet"
      href="https://web.archive.org/web/20250418055804cs_/https://fonts.googleapis.com/css2?family=Commissioner:wght@400;500&amp;family=Playfair+Display:wght@400;600&amp;display=swap"
      media="print"
      onload="this.media='all'"
    />

    <noscript>
      <link
        rel="stylesheet"
        href="https://web.archive.org/web/20250418055804cs_/https://fonts.googleapis.com/css2?family=Commissioner:wght@400;500&amp;family=Playfair+Display:wght@400;600&amp;display=swap"
      />
    </noscript>

    <script>
      document.documentElement.classList.add("js");
      document.documentElement.classList.remove("no-js");
    </script>

    <meta name="robots" content="max-image-preview:large" />
    <style>
      img:is([sizes="auto" i], [sizes^="auto," i]) {
        contain-intrinsic-size: 3000px 1500px;
      }
    </style>
    <style id="classic-theme-styles-inline-css" type="text/css">
      /*! This file is auto-generated */
      .wp-block-button__link {
        color: #fff;
        background-color: #32373c;
        border-radius: 9999px;
        box-shadow: none;
        text-decoration: none;
        padding: calc(0.667em + 2px) calc(1.333em + 2px);
        font-size: 1.125em;
      }
      .wp-block-file__button {
        background: #32373c;
        color: #fff;
        text-decoration: none;
      }
    </style>
    <style id="global-styles-inline-css" type="text/css">
      :root {
        --wp--preset--aspect-ratio--square: 1;
        --wp--preset--aspect-ratio--4-3: 4/3;
        --wp--preset--aspect-ratio--3-4: 3/4;
        --wp--preset--aspect-ratio--3-2: 3/2;
        --wp--preset--aspect-ratio--2-3: 2/3;
        --wp--preset--aspect-ratio--16-9: 16/9;
        --wp--preset--aspect-ratio--9-16: 9/16;
        --wp--preset--color--black: #000000;
        --wp--preset--color--cyan-bluish-gray: #abb8c3;
        --wp--preset--color--white: #ffffff;
        --wp--preset--color--pale-pink: #f78da7;
        --wp--preset--color--vivid-red: #cf2e2e;
        --wp--preset--color--luminous-vivid-orange: #ff6900;
        --wp--preset--color--luminous-vivid-amber: #fcb900;
        --wp--preset--color--light-green-cyan: #7bdcb5;
        --wp--preset--color--vivid-green-cyan: #00d084;
        --wp--preset--color--pale-cyan-blue: #8ed1fc;
        --wp--preset--color--vivid-cyan-blue: #0693e3;
        --wp--preset--color--vivid-purple: #9b51e0;
        --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(
          135deg,
          rgba(6, 147, 227, 1) 0%,
          rgb(155, 81, 224) 100%
        );
        --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(
          135deg,
          rgb(122, 220, 180) 0%,
          rgb(0, 208, 130) 100%
        );
        --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(
          135deg,
          rgba(252, 185, 0, 1) 0%,
          rgba(255, 105, 0, 1) 100%
        );
        --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(
          135deg,
          rgba(255, 105, 0, 1) 0%,
          rgb(207, 46, 46) 100%
        );
        --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(
          135deg,
          rgb(238, 238, 238) 0%,
          rgb(169, 184, 195) 100%
        );
        --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(
          135deg,
          rgb(74, 234, 220) 0%,
          rgb(151, 120, 209) 20%,
          rgb(207, 42, 186) 40%,
          rgb(238, 44, 130) 60%,
          rgb(251, 105, 98) 80%,
          rgb(254, 248, 76) 100%
        );
        --wp--preset--gradient--blush-light-purple: linear-gradient(
          135deg,
          rgb(255, 206, 236) 0%,
          rgb(152, 150, 240) 100%
        );
        --wp--preset--gradient--blush-bordeaux: linear-gradient(
          135deg,
          rgb(254, 205, 165) 0%,
          rgb(254, 45, 45) 50%,
          rgb(107, 0, 62) 100%
        );
        --wp--preset--gradient--luminous-dusk: linear-gradient(
          135deg,
          rgb(255, 203, 112) 0%,
          rgb(199, 81, 192) 50%,
          rgb(65, 88, 208) 100%
        );
        --wp--preset--gradient--pale-ocean: linear-gradient(
          135deg,
          rgb(255, 245, 203) 0%,
          rgb(182, 227, 212) 50%,
          rgb(51, 167, 181) 100%
        );
        --wp--preset--gradient--electric-grass: linear-gradient(
          135deg,
          rgb(202, 248, 128) 0%,
          rgb(113, 206, 126) 100%
        );
        --wp--preset--gradient--midnight: linear-gradient(
          135deg,
          rgb(2, 3, 129) 0%,
          rgb(40, 116, 252) 100%
        );
        --wp--preset--font-size--small: 13px;
        --wp--preset--font-size--medium: 20px;
        --wp--preset--font-size--large: 36px;
        --wp--preset--font-size--x-large: 42px;
        --wp--preset--spacing--20: 0.44rem;
        --wp--preset--spacing--30: 0.67rem;
        --wp--preset--spacing--40: 1rem;
        --wp--preset--spacing--50: 1.5rem;
        --wp--preset--spacing--60: 2.25rem;
        --wp--preset--spacing--70: 3.38rem;
        --wp--preset--spacing--80: 5.06rem;
        --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
        --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
        --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
        --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1),
          6px 6px rgba(0, 0, 0, 1);
        --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
      }
      :where(.is-layout-flex) {
        gap: 0.5em;
      }
      :where(.is-layout-grid) {
        gap: 0.5em;
      }
      body .is-layout-flex {
        display: flex;
      }
      .is-layout-flex {
        flex-wrap: wrap;
        align-items: center;
      }
      .is-layout-flex > :is(*, div) {
        margin: 0;
      }
      body .is-layout-grid {
        display: grid;
      }
      .is-layout-grid > :is(*, div) {
        margin: 0;
      }
      :where(.wp-block-columns.is-layout-flex) {
        gap: 2em;
      }
      :where(.wp-block-columns.is-layout-grid) {
        gap: 2em;
      }
      :where(.wp-block-post-template.is-layout-flex) {
        gap: 1.25em;
      }
      :where(.wp-block-post-template.is-layout-grid) {
        gap: 1.25em;
      }
      .has-black-color {
        color: var(--wp--preset--color--black) !important;
      }
      .has-cyan-bluish-gray-color {
        color: var(--wp--preset--color--cyan-bluish-gray) !important;
      }
      .has-white-color {
        color: var(--wp--preset--color--white) !important;
      }
      .has-pale-pink-color {
        color: var(--wp--preset--color--pale-pink) !important;
      }
      .has-vivid-red-color {
        color: var(--wp--preset--color--vivid-red) !important;
      }
      .has-luminous-vivid-orange-color {
        color: var(--wp--preset--color--luminous-vivid-orange) !important;
      }
      .has-luminous-vivid-amber-color {
        color: var(--wp--preset--color--luminous-vivid-amber) !important;
      }
      .has-light-green-cyan-color {
        color: var(--wp--preset--color--light-green-cyan) !important;
      }
      .has-vivid-green-cyan-color {
        color: var(--wp--preset--color--vivid-green-cyan) !important;
      }
      .has-pale-cyan-blue-color {
        color: var(--wp--preset--color--pale-cyan-blue) !important;
      }
      .has-vivid-cyan-blue-color {
        color: var(--wp--preset--color--vivid-cyan-blue) !important;
      }
      .has-vivid-purple-color {
        color: var(--wp--preset--color--vivid-purple) !important;
      }
      .has-black-background-color {
        background-color: var(--wp--preset--color--black) !important;
      }
      .has-cyan-bluish-gray-background-color {
        background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
      }
      .has-white-background-color {
        background-color: var(--wp--preset--color--white) !important;
      }
      .has-pale-pink-background-color {
        background-color: var(--wp--preset--color--pale-pink) !important;
      }
      .has-vivid-red-background-color {
        background-color: var(--wp--preset--color--vivid-red) !important;
      }
      .has-luminous-vivid-orange-background-color {
        background-color: var(
          --wp--preset--color--luminous-vivid-orange
        ) !important;
      }
      .has-luminous-vivid-amber-background-color {
        background-color: var(
          --wp--preset--color--luminous-vivid-amber
        ) !important;
      }
      .has-light-green-cyan-background-color {
        background-color: var(--wp--preset--color--light-green-cyan) !important;
      }
      .has-vivid-green-cyan-background-color {
        background-color: var(--wp--preset--color--vivid-green-cyan) !important;
      }
      .has-pale-cyan-blue-background-color {
        background-color: var(--wp--preset--color--pale-cyan-blue) !important;
      }
      .has-vivid-cyan-blue-background-color {
        background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
      }
      .has-vivid-purple-background-color {
        background-color: var(--wp--preset--color--vivid-purple) !important;
      }
      .has-black-border-color {
        border-color: var(--wp--preset--color--black) !important;
      }
      .has-cyan-bluish-gray-border-color {
        border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
      }
      .has-white-border-color {
        border-color: var(--wp--preset--color--white) !important;
      }
      .has-pale-pink-border-color {
        border-color: var(--wp--preset--color--pale-pink) !important;
      }
      .has-vivid-red-border-color {
        border-color: var(--wp--preset--color--vivid-red) !important;
      }
      .has-luminous-vivid-orange-border-color {
        border-color: var(
          --wp--preset--color--luminous-vivid-orange
        ) !important;
      }
      .has-luminous-vivid-amber-border-color {
        border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
      }
      .has-light-green-cyan-border-color {
        border-color: var(--wp--preset--color--light-green-cyan) !important;
      }
      .has-vivid-green-cyan-border-color {
        border-color: var(--wp--preset--color--vivid-green-cyan) !important;
      }
      .has-pale-cyan-blue-border-color {
        border-color: var(--wp--preset--color--pale-cyan-blue) !important;
      }
      .has-vivid-cyan-blue-border-color {
        border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
      }
      .has-vivid-purple-border-color {
        border-color: var(--wp--preset--color--vivid-purple) !important;
      }
      .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
        background: var(
          --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple
        ) !important;
      }
      .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
        background: var(
          --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan
        ) !important;
      }
      .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
        background: var(
          --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange
        ) !important;
      }
      .has-luminous-vivid-orange-to-vivid-red-gradient-background {
        background: var(
          --wp--preset--gradient--luminous-vivid-orange-to-vivid-red
        ) !important;
      }
      .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
        background: var(
          --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray
        ) !important;
      }
      .has-cool-to-warm-spectrum-gradient-background {
        background: var(
          --wp--preset--gradient--cool-to-warm-spectrum
        ) !important;
      }
      .has-blush-light-purple-gradient-background {
        background: var(--wp--preset--gradient--blush-light-purple) !important;
      }
      .has-blush-bordeaux-gradient-background {
        background: var(--wp--preset--gradient--blush-bordeaux) !important;
      }
      .has-luminous-dusk-gradient-background {
        background: var(--wp--preset--gradient--luminous-dusk) !important;
      }
      .has-pale-ocean-gradient-background {
        background: var(--wp--preset--gradient--pale-ocean) !important;
      }
      .has-electric-grass-gradient-background {
        background: var(--wp--preset--gradient--electric-grass) !important;
      }
      .has-midnight-gradient-background {
        background: var(--wp--preset--gradient--midnight) !important;
      }
      .has-small-font-size {
        font-size: var(--wp--preset--font-size--small) !important;
      }
      .has-medium-font-size {
        font-size: var(--wp--preset--font-size--medium) !important;
      }
      .has-large-font-size {
        font-size: var(--wp--preset--font-size--large) !important;
      }
      .has-x-large-font-size {
        font-size: var(--wp--preset--font-size--x-large) !important;
      }
      :where(.wp-block-post-template.is-layout-flex) {
        gap: 1.25em;
      }
      :where(.wp-block-post-template.is-layout-grid) {
        gap: 1.25em;
      }
      :where(.wp-block-columns.is-layout-flex) {
        gap: 2em;
      }
      :where(.wp-block-columns.is-layout-grid) {
        gap: 2em;
      }
      :root :where(.wp-block-pullquote) {
        font-size: 1.5em;
        line-height: 1.6;
      }
    </style>
    <link
      rel="stylesheet"
      id="wp-components-css"
      href="https://web.archive.org/web/20250418055804cs_/https://cavig.com/wp-includes/css/dist/components/style.min.css?ver=6.7.2"
      type="text/css"
      media="all"
    />
    <link
      rel="stylesheet"
      id="godaddy-styles-css"
      href="https://web.archive.org/web/20250418055804cs_/https://cavig.com/wp-content/mu-plugins/vendor/wpex/godaddy-launch/includes/Dependencies/GoDaddy/Styles/build/latest.css?ver=2.0.2"
      type="text/css"
      media="all"
    />
    <link rel="https://api.w.org/" href="https://cavig.com/wp-json/" />
    <link
      rel="alternate"
      title="JSON"
      type="application/json"
      href="https://web.archive.org/web/20250418055804/https://cavig.com/wp-json/wp/v2/pages/140"
    />
    <link
      rel="canonical"
      href="https://web.archive.org/web/20250418055804/https://cavig.com/about-us/"
    />
    <link
      rel="shortlink"
      href="https://web.archive.org/web/20250418055804/https://cavig.com/?p=140"
    />
    <link
      rel="alternate"
      title="oEmbed (JSON)"
      type="application/json+oembed"
      href="https://web.archive.org/web/20250418055804/https://cavig.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fcavig.com%2Fabout-us%2F"
    />
    <link
      rel="alternate"
      title="oEmbed (XML)"
      type="text/xml+oembed"
      href="https://web.archive.org/web/20250418055804/https://cavig.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fcavig.com%2Fabout-us%2F&amp;format=xml"
    />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="57x57"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/apple-touch-icon-57x57.png"
    />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="114x114"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/apple-touch-icon-114x114.png"
    />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="72x72"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/apple-touch-icon-72x72.png"
    />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="144x144"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/apple-touch-icon-144x144.png"
    />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="60x60"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/apple-touch-icon-60x60.png"
    />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="120x120"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/apple-touch-icon-120x120.png"
    />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="76x76"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/apple-touch-icon-76x76.png"
    />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="152x152"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/apple-touch-icon-152x152.png"
    />
    <link
      rel="icon"
      type="image/png"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/favicon-196x196.png"
      sizes="196x196"
    />
    <link
      rel="icon"
      type="image/png"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/favicon-96x96.png"
      sizes="96x96"
    />
    <link
      rel="icon"
      type="image/png"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/favicon-32x32.png"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/favicon-16x16.png"
      sizes="16x16"
    />
    <link
      rel="icon"
      type="image/png"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/icons/favicon-128.png"
      sizes="128x128"
    />
    <meta name="application-name" content=" " />
    <meta name="msapplication-TileColor" content="#FFFFFF" />
    <meta
      name="msapplication-TileImage"
      content="https://cavig.com/wp-content/themes/cavig/icons/mstile-144x144.png"
    />
    <meta
      name="msapplication-square70x70logo"
      content="https://cavig.com/wp-content/themes/cavig/icons/mstile-70x70.png"
    />
    <meta
      name="msapplication-square150x150logo"
      content="https://cavig.com/wp-content/themes/cavig/icons/mstile-150x150.png"
    />
    <meta
      name="msapplication-wide310x150logo"
      content="https://cavig.com/wp-content/themes/cavig/icons/mstile-310x150.png"
    />
    <meta
      name="msapplication-square310x310logo"
      content="https://cavig.com/wp-content/themes/cavig/icons/mstile-310x310.png"
    />
    <meta
      name="description"
      content="  Auto Draft What is CAV Investment Group Classifieds Ad Ventures Investment Group provides funding, strategy, advice, guidance and ongoing operational support to accelerate growth and help companies solve some of the world’s biggest challenges. Our area of deepest expertise lies in the online real estate classifieds industry with a particular focus on markets characterized…"
    />

    <meta property="og:title" content="About us – CAV Investment Group" />
    <meta
      property="og:url"
      content="https://web.archive.org/web/20250418055804/https://cavig.com/about-us/"
    />
    <meta property="og:site_name" content="CAV Investment Group" />
    <meta
      property="og:description"
      content="  Auto Draft What is CAV Investment Group Classifieds Ad Ventures Investment Group provides funding, strategy, advice, guidance and ongoing operational support to accelerate growth and help companies solve some of the world’s biggest challenges. Our area of deepest expertise lies in the online real estate classifieds industry with a particular focus on markets characterized…"
    />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@" />
    <meta name="twitter:site:id" content="" />
    <meta name="twitter:creator" content="" />
    <meta name="twitter:creator:id" content="" />
    <meta name="twitter:title" content="About us" />
    <meta
      name="twitter:description"
      content="  Auto Draft What is CAV Investment Group Classifieds Ad Ventures Investment Group provides funding, strategy, advice, guidance and ongoing operational support to accelerate growth and help companies solve some of the world’s biggest challenges. Our area of deepest expertise lies in the online real estate classifieds industry with a particular focus on markets characterized…"
    />
    <style>
      .empresa-contenido,
      .empresas-filtro,
      .texto.texto--ancho-completo > .inner,
      .texto.texto--ancho-estrecho.texto--alineacion-izquierda > .inner,
      .titulo.titulo--ancho-completo > .inner,
      .titulo.titulo--ancho-estrecho.titulo--alineacion-izquierda > .inner {
        width: Min(1030, 90vw);
      }
      .texto.texto--ancho-estrecho.texto--alineacion-centrado > .inner,
      .titulo.titulo--ancho-estrecho.titulo--alineacion-centrado > .inner {
        width: Min(810, 90vw);
      }
      .cabeceraPrincipal > .inner {
        --padding: Max(1.33333rem, Min(2.5vw, 2rem));
        padding: var(--padding) 0;
        align-items: center;
      }
      .cabeceraPrincipal-logo img {
        display: block;
        margin: 0 auto;
      }
      .cabeceraPrincipal-menu {
        margin: calc(var(--padding) - 0.25em) 0 0;
        text-transform: uppercase;
        font-size: 0.8em;
      }
      .cabeceraPrincipal-menu ul {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        list-style: none;
        margin: 0;
        padding: 0;
      }
      .cabeceraPrincipal-menu li {
        margin: 0.25em 1em;
      }
      .cabeceraPrincipal-menu a {
        color: #34779b;
        text-decoration: none;
      }
      .cabeceraPrincipal-menu li[class*="current"] a,
      body.single-companies #menu-item-104 a {
        color: #1e1f1f;
      }
      @media only screen and (min-width: 48rem) {
        .cabeceraPrincipal > .inner {
          display: flex;
          justify-content: space-between;
        }
        .cabeceraPrincipal-logo img {
          max-width: 25vw;
        }
        .cabeceraPrincipal-menu {
          margin: 0;
        }
        .cabeceraPrincipal-menu li {
          margin: 0 0 0 2em;
        }
      }
      .empresa-cabecera-imagen img {
        display: block;
        width: 100%;
        height: Max(13.54167rem, Min(25.39062vw, 20.3125rem));
        object-fit: cover;
      }
      .empresa-contenido-logo {
        --tamano: Max(7.5rem, Min(14.53125vw, 11.625rem));
        width: var(--tamano);
        height: var(--tamano);
        margin-top: calc(var(--tamano) / -2);
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #34779b;
        background: #fff;
        padding: 0.5em;
      }
      .empresa-contenido-titulo {
        color: #34779b;
        margin: 0.5em 0;
      }
      .empresa-contenido-enlaces {
        margin: 1em -1em;
      }
      .empresa-contenido-enlaces a {
        margin: 1em;
      }
      .empresa-contenido-enlaces a:after {
        transform: rotate(-90deg) translateX(1px);
      }
      .empresa-contenido-meta {
        border-top: 1px solid #567382;
        margin-top: 2em;
        padding-top: 2em;
      }
      .empresa-contenido-meta dl {
        display: flex;
        justify-content: space-around;
        text-align: center;
        margin: 0;
      }
      .empresa-contenido-meta dl div {
        margin-bottom: 1.5em;
      }
      .empresa-contenido-meta dl dd,
      .empresa-contenido-meta dl dt {
        margin: 0;
        padding: 0;
      }
      .empresa-contenido-meta dl dt {
        color: #34779b;
        text-transform: uppercase;
        font-size: 0.6875em;
        margin-bottom: 0.25em;
      }
      @media only screen and (min-width: 48rem) {
        .empresa-contenido-enlaces {
          display: flex;
          justify-content: space-between;
        }
      }
      @media only screen and (min-width: 64rem) {
        .empresa-contenido {
          display: grid;
          grid-template-columns: max-content 1fr max-content;
          grid-column-gap: 1.875rem;
        }
        .empresa-contenido-logo {
          grid-column: 1;
          grid-row: 1/5;
        }
        .empresa-contenido-titulo {
          grid-column: 2;
          grid-row: 1;
          margin-top: 1.4em;
        }
        .empresa-contenido-texto {
          grid-column: 2;
          grid-row: 2;
        }
        .empresa-contenido-enlaces {
          grid-column: 2;
          grid-row: 4;
        }
        .empresa-contenido-meta {
          grid-column: 3;
          grid-row: 2/4;
          border-top: 0;
          padding-top: 0;
          margin-top: 0;
          padding-left: Max(2.91667rem, Min(5.46875vw, 4.375rem));
          margin-left: Max(2.91667rem, Min(5.46875vw, 4.375rem));
          border-left: 1px solid #567382;
        }
        .empresa-contenido-meta dl {
          display: block;
          text-align: left;
        }
      }
      .piePrincipal {
        background: var(--fondo);
      }
      .piePrincipal > .inner {
        padding: 2em 0;
        border-top: 1px solid #34779b;
        display: grid;
        grid-gap: 1.5em;
        align-items: center;
        text-align: center;
      }
      .piePrincipal-logo img {
        display: block;
        margin: 0 auto;
      }
      .piePrincipal-menu ul {
        list-style: none;
        margin: 0 0 -0.5em;
        padding: 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
      }
      .piePrincipal-menu li {
        margin: 0 0.5em 0.5em;
      }
      .piePrincipal-menu a {
        color: #34779b;
        text-decoration: none;
      }
      .piePrincipal-contacto {
        grid-row: 2;
        display: grid;
        grid-gap: 1em;
      }
      .piePrincipal-contacto-telefono a {
        color: #34779b;
        text-decoration: none;
      }
      .piePrincipal-contacto-redes .redes {
        display: flex;
        justify-content: center;
      }
      .piePrincipal-contacto-redes .redes a {
        color: #34779b;
        display: flex;
        align-items: flex-end;
        margin-left: 0.5em;
      }
      @media only screen and (min-width: 48rem) {
        .piePrincipal > .inner {
          grid-template-columns: max-content 1fr;
          align-items: start;
          text-align: left;
        }
        .piePrincipal-logo {
          grid-column: 1;
          grid-row: 1;
        }
        .piePrincipal-contacto {
          grid-row: 1;
          grid-column: 2;
          display: grid;
          grid-template-columns: 1fr max-content;
        }
        .piePrincipal-contacto-direccion {
          grid-column: 1/3;
        }
        .piePrincipal-menu ul {
          justify-content: flex-end;
        }
      }
      @media only screen and (min-width: 64rem) {
        .piePrincipal > .inner {
          grid-template-columns: max-content max-content 1fr;
          grid-column-gap: Max(4.16667rem, Min(7.8125vw, 6.25rem));
          align-items: center;
        }
        .piePrincipal-logo {
          grid-column: 1;
          grid-row: 1;
        }
        .piePrincipal-copyright {
          grid-column: 2;
          grid-row: 1;
        }
        .piePrincipal-contacto {
          grid-column: 3;
          grid-gap: 0;
          grid-column-gap: 1em;
          height: 100%;
          grid-template-columns: 1fr max-content;
          align-items: center;
        }
        .piePrincipal-contacto-direccion {
          align-items: center;
          grid-column: 1;
        }
        .piePrincipal-contacto-telefono {
          grid-column: 2;
          text-align: right;
          margin-bottom: 1em;
        }
        .piePrincipal-contacto-redes {
          grid-column: 2;
        }
        .redes {
          justify-content: flex-end;
        }
        .piePrincipal.con-telefono .piePrincipal-contacto-redes {
          align-self: end;
          grid-row: 2;
        }
        .piePrincipal.con-telefono .piePrincipal-contacto-direccion {
          grid-row: 1/3;
        }
      }
      *,
      :after,
      :before {
        box-sizing: inherit;
      }
      html {
        box-sizing: border-box;
        color: #4d4d4d;
        font-family: Montserrat, sans-serif;
        font-size: 100%;
        font-weight: 400;
        line-height: 1.5;
        -webkit-font-smoothing: antialiased;
      }
      body,
      html {
        margin: 0;
        padding: 0;
      }
      body {
        overflow-y: scroll;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }
      body.admin-bar {
        min-height: calc(100vh - 46px);
      }
      @media only screen and (min-width: 782px) {
        body.admin-bar {
          min-height: calc(100vh - 32px);
        }
      }
      img {
        max-width: 100%;
        height: auto;
      }
      .contenedor-video {
        clear: both;
        position: relative;
        padding-bottom: 56.25%;
        display: block;
      }
      .contenedor-video > * {
        position: absolute;
        width: 100%;
        height: 100%;
      }
      h1,
      h2,
      h3,
      h4,
      h6 {
        font-family: Montserrat, sans-serif;
      }
      .aligncenter {
        display: block;
        margin: 0 auto;
      }
      @media only screen and (min-width: 48rem) {
        .alignleft,
        .alignright {
          margin-bottom: Max(2.5rem, Min(4.6875vw, 3.75rem));
        }
        .alignleft {
          float: left;
          margin-right: Max(3.33333rem, Min(6.25vw, 5rem));
        }
        .alignright {
          float: right;
          margin-left: Max(3.33333rem, Min(6.25vw, 5rem));
        }
      }
      .screen-reader-text {
        position: absolute;
        left: -999em;
      }
      .contenedor {
        flex: 1 0 auto;
      }
      .js .preload * {
        transition: none !important;
      }
      body.con-raton button,
      body.con-raton div {
        outline: none;
      }
      a {
        color: #34779b;
      }
      .contenedor {
        --margen: Max(4.16667rem, Min(7.8125vw, 6.25rem));
      }
      .bloque {
        margin-top: var(--margen);
        margin-bottom: var(--margen);
      }
      .bloque:first-child {
        margin-top: 0;
      }
      .bloque.con-fondo {
        background: var(--fondo);
        padding-top: Max(4.16667rem, Min(7.8125vw, 6.25rem));
        padding-bottom: Max(4.16667rem, Min(7.8125vw, 6.25rem));
        margin: 0;
      }
      .bloque.con-fondo + .con-fondo {
        padding-top: 0;
      }
      .cabecera {
        display: grid;
        color: #fff;
        margin-bottom: calc(var(--margen) / -2);
      }
      .cabecera:not(.cabecera--con-imagen-de-fondo) {
        background: linear-gradient(0deg, #347596, #33a6c2);
        min-height: Max(11.66667rem, Min(21.875vw, 17.5rem));
      }
      .cabecera > .inner {
        grid-column: 1;
        grid-row: 1;
        align-self: center;
        z-index: 2;
      }
      .cabecera-imagen_de_fondo {
        grid-column: 1;
        grid-row: 1;
        z-index: 1;
        position: relative;
        max-height: 28.125rem;
      }
      .cabecera-imagen_de_fondo:after {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          0deg,
          rgba(19, 46, 60, 0.2) 17.5%,
          rgba(19, 46, 60, 0) 75%
        );
      }
      .cabecera-imagen_de_fondo img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .cabecera-titulo {
        padding-bottom: 0.5em;
      }
      .cabecera-titulo > h1 {
        max-width: 9.5em;
      }
      .cabecera-titulo:after {
        content: "";
        display: block;
        width: 10%;
        height: 4px;
        background: currentColor;
      }
      .cabecera:not(.cabecera--con-imagen-de-fondo) .cabecera-titulo:after {
        margin: 0 auto;
      }
      .cabecera:not(.cabecera--con-imagen-de-fondo) .cabecera-titulo > h1 {
        margin: 0 auto 0.5em;
        text-align: center;
      }
      .cabecera--con-imagen-de-fondo .cabecera-titulo:after {
        margin: 0 0 0.5em;
        transform: translateX(-50%);
      }
      .cabecera-introduccion {
        font-size: Max(1rem, Min(3.13vw, 1.5rem));
        font-family: Montserrat, sans-serif;
        line-height: 1.2;
        max-width: 32em;
        margin-bottom: 1.5em;
      }
      .cabecera-boton {
        margin-bottom: 2em;
      }
      .texto.anterior-titulo {
        margin-top: calc(var(--margen) * -1);
      }
      .texto.texto--ancho-estrecho.texto--alineacion-izquierda
        > .inner
        > .texto-contenido {
        max-width: Min(62.5rem, 90vw);
      }
      .texto-titulo {
        color: #34779b;
        margin: 0 0 0.6em;
        max-width: 20.5em;
      }
      .texto-imagen img {
        display: block;
        width: 100%;
      }
      .texto-enlace > a {
        font-weight: 500;
        text-decoration: none;
        color: #4d4d4d;
        padding-left: 1.5em;
        padding-bottom: 0.25em;
        display: inline-block;
        background: linear-gradient(90deg, #fff, #34779b 50%, #34779b 0)
          no-repeat 100% 1.5em;
        background-size: 200% 100%;
        transition: 0.3s;
      }
      .texto-enlace > a:hover {
        background-position: 0 1.5em;
      }
      .texto:not(.texto--con-imagen) .texto-texto {
        margin: 2em 0;
      }
      .texto-texto li {
        margin-bottom: 1em;
      }
      @media only screen and (min-width: 48rem) {
        .texto--con-imagen > .inner > .texto-contenido {
          display: grid;
          grid-template-columns: 35% 1fr;
          grid-template-rows: max-content 1fr max-content;
          grid-column-gap: 1.5em;
        }
        .texto-titulo {
          grid-column: 1/3;
          grid-row: 1;
        }
        .texto-imagen {
          grid-column: 1;
          grid-row: 2/4;
        }
      }
      @media only screen and (min-width: 64rem) {
        .texto--con-imagen > .inner > .contenido {
          grid-template-rows: max-content max-content max-content;
        }
        .texto--con-imagen .texto-titulo {
          grid-column: 2;
          grid-row: 1;
        }
        .texto--con-imagen .texto-texto {
          grid-column: 2;
          grid-row: 2;
        }
        .texto--con-imagen .texto-texto p:first-child {
          margin-top: -0.25em;
        }
        .texto--con-imagen .texto-imagen {
          grid-column: 1;
          grid-row: 1/4;
          float: none;
          max-width: none;
          margin: 0;
        }
        .texto--con-imagen .texto-imagen img {
          object-fit: cover;
        }
      }
      .empresas {
        margin-top: 5em;
      }
      .empresas-filtro {
        margin-bottom: 5em;
      }
      .empresas-filtro-titulo {
        font-family: Montserrat, sans-serif;
        margin: 0 0 1em;
      }
      .empresas-filtro-campos {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
      }
      .empresas-filtro-campos p {
        margin: 0 1em 1em 0;
      }
      .empresas-filtro-campos label {
        position: absolute;
        left: -999em;
      }
      .empresas-filtro-campos select {
        color: #34779b;
        font: inherit;
        background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='11' height='7'%3E%3Cpath d='M10.246.668a1 1 0 00-1.41 0l-3.59 3.54-3.54-3.54a1 1 0 10-1.41 1.42l4.24 4.24a1 1 0 001.42 0l4.29-4.24a1 1 0 000-1.42z' fill='%233B87AF'/%3E%3C/svg%3E")
          right 5px center no-repeat;
        padding-right: calc(1rem + 1em);
        -webkit-appearance: none;
        appearance: none;
        border: 0;
      }
      .empresas-filtro-campos button {
        border: 1px solid !important;
        padding: 0.5em 1em !important;
      }
      .empresas-filtro-campos button:after {
        content: none !important;
      }
      .empresas-lista {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(15.625rem, 1fr));
        position: relative;
      }
      .empresas-lista:after,
      .empresas-lista:before {
        content: "";
        position: absolute;
        display: block;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #fff;
        z-index: 2;
      }
      .empresas-lista:before {
        left: -1px;
      }
      .empresas-lista:after {
        right: 0;
      }
      .empresas-empresa {
        position: relative;
        border: 1px solid #567382;
        margin-left: -1px;
        margin-bottom: -1px;
      }
      .empresas-empresa.animado:not(.is-visible) {
        transform: none;
      }
      .empresas-empresa.animado:not(.is-visible) .empresas-empresa-descripcion,
      .empresas-empresa.animado:not(.is-visible) .empresas-empresa-logo {
        transform: translateY(50px);
        opacity: 0;
      }
      .empresas-empresa-enlace {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        text-indent: 100%;
        white-space: nowrap;
        overflow: hidden;
      }
      .empresas-empresa > .inner {
        padding: Max(2.08333rem, Min(3.90625vw, 3.125rem));
        display: grid;
        grid-template-rows: max-content 1fr;
        height: 100%;
      }
      .empresas-empresa-logo {
        margin-bottom: 1em;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: 1s;
        padding-bottom: 50%;
        position: relative;
        pointer-events: none;
      }
      .empresas-empresa-logo > .inner {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }
      .empresas-empresa-logo picture {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }
      .empresas-empresa-logo img {
        margin: 0 auto;
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      .empresas-empresa-descripcion,
      .empresas-empresa-hover {
        pointer-events: none;
        grid-row: 2;
        grid-column: 1;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: 1s;
        transition-delay: 0.3s;
      }
      .empresas-empresa-hover {
        background: #fff;
        opacity: 0;
        transition: 0.3s;
      }
      .empresas-empresa-enlace:focus + .inner > .empresas-empresa-hover,
      .empresas-empresa:hover .empresas-empresa-hover {
        opacity: 1;
      }
      @media only screen and (min-width: 64rem) {
        .empresas-filtro {
          display: flex;
          align-items: center;
        }
        .empresas-filtro-titulo {
          margin: 0 2em 0 0;
        }
        .empresas-filtro-campos p {
          margin-bottom: 0;
        }
      }
      form > div[class$="-formulario-wrapper"] {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
      }
      form .error {
        font-size: 13px;
        line-height: 1.3;
        display: inline-block;
        color: #bc0000;
        padding: 0.5em 1em;
      }
      form p {
        position: relative;
        line-height: 1.2;
        margin: 0;
      }
      form .campo--file div,
      form input:not([type="checkbox"]):not([type="submit"]):not([type="file"]),
      form select,
      form textarea {
        font: inherit;
        width: 100%;
        display: block;
        padding: 0.5em 1em;
      }
      form input[type="file"] {
        opacity: 0;
        position: absolute;
      }
      form .campo {
        position: relative;
        margin-bottom: 0.5em;
        display: block;
        flex: 0 0 auto;
        width: 100%;
      }
      form .campo--file div {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      form .campo:not(.campo--checkbox) {
        position: relative;
      }
      form .campo:not(.campo--checkbox) > label:first-child {
        display: none;
      }
      form .campo:not(.campo--checkbox) > label {
        position: absolute;
        transition: 0.3s ease;
        top: -1.4em;
        left: 0;
        font-size: 0.7em;
      }
      form .campo:not(.campo--checkbox) > label.is-inactiva {
        top: 0.5em;
        left: 1em;
        opacity: 0.55;
        font-size: 1em;
      }
      form label {
        margin-bottom: 0.5em;
        display: block;
      }
      form label,
      form label > a {
        color: currentColor;
      }
      form p .error {
        position: absolute;
        right: 0;
        bottom: 100%;
        margin-bottom: 2px;
      }
      form p.campo--checkbox .error {
        left: 25px;
        bottom: auto;
        top: 100%;
      }
      form input.miel {
        position: absolute;
        left: -999em;
      }
      form .campo--checkbox {
        position: relative;
      }
      form .campo--checkbox input {
        opacity: 0;
        position: absolute;
      }
      form .campo--checkbox label {
        padding-left: 25px;
        display: block;
        position: relative;
      }
      form .campo--checkbox label:before {
        content: "";
        left: 0;
        top: calc(50% - 0.6em);
        width: 1.2em;
        height: 1.2em;
        border: 1px solid;
        vertical-align: middle;
        margin-right: 0.5em;
        transition: 0.3s;
        position: absolute;
      }
      form .campo--checkbox input:checked + label:before {
        background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 15'%3E%3Cpath d='M2 7l6 6 5-11' stroke-width='2' stroke='%23567382' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
      }
      form .campo--checkbox input:focus + label:before {
        outline: 2px solid #34779b;
      }
      form .campo--submit {
        text-align: right;
      }
      @media only screen and (min-width: 480px) {
        form .campo {
          margin-bottom: 1.25rem;
        }
        form .campo--25 {
          width: calc(25% - 0.625rem);
        }
        form .campo--33 {
          width: calc(33% - 0.625rem);
        }
        form .campo--50 {
          width: calc(50% - 0.625rem);
        }
        form .campo--66 {
          width: calc(66% - 0.625rem);
        }
        form .campo--75 {
          width: calc(75% - 0.625rem);
        }
        form .campo--100 {
          width: 100%;
        }
        form .campo--auto {
          flex: 1;
          width: auto;
        }
      }
      .contacto-titulo {
        margin: 0 0 1em;
        color: #34779b;
      }
      .formulario-titulo {
        margin: 0 0 0.5em;
        color: #567382;
      }
      .contacto-formulario .campo--file {
        transition: 0.3s;
      }
      .contacto-formulario .campo--file.archivo-encima {
        background: rgba(52, 119, 155, 0.3);
      }
      .contacto-formulario .campo--file,
      .contacto-formulario .campo--input,
      .contacto-formulario .campo--textarea {
        border: 1px solid #34779b;
        border-radius: 0.1875rem;
      }
      .contacto-formulario input,
      .contacto-formulario textarea {
        border: 0;
        background: transparent;
      }
      .contacto-formulario textarea {
        height: 8em;
      }
      .contacto-formulario .campo:not(.campo--checkbox) label {
        color: #567382;
      }
      @media only screen and (min-width: 64rem) {
        .contacto:not(.contacto--sin-texto) > .inner {
          display: grid;
          grid-template-columns: 1fr 1fr;
          grid-column-gap: 2em;
        }
        .contacto--sin-texto form {
          max-width: 39.0625rem;
          margin-left: auto;
          margin-right: auto;
        }
        .contacto-titulo {
          grid-column: 1/3;
        }
        .contacto-texto p {
          max-width: 25em;
        }
        .contacto-texto p:first-child {
          margin-top: 0;
        }
      }
      .caracteristicas-caracteristica {
        margin-bottom: 3em;
      }
      .caracteristicas-caracteristica-imagen img {
        display: block;
        width: 100%;
      }
      .caracteristicas-caracteristica-titulo {
        color: #34779b;
      }
      @media only screen and (min-width: 48rem) {
        .caracteristicas-caracteristica {
          display: grid;
          width: Min(64.375rem, 90vw);
          grid-template-columns: 1fr 1fr;
          grid-column-gap: 2em;
        }
        .caracteristicas-caracteristica-titulo {
          grid-column: 1/3;
        }
        .caracteristicas-caracteristica-imagen.animado:not(.is-visible),
        .caracteristicas-caracteristica-titulo.animado:not(.is-visible) {
          transform: translateX(-50px);
        }
        .caracteristicas-caracteristica-texto {
          align-self: end;
          padding-bottom: 1em;
        }
        .caracteristicas-caracteristica:nth-child(2n) {
          margin-left: auto;
        }
        .caracteristicas-caracteristica:nth-child(2n)
          .caracteristicas-caracteristica-titulo {
          text-align: right;
        }
        .caracteristicas-caracteristica:nth-child(2n)
          .caracteristicas-caracteristica-titulo.animado:not(.is-visible) {
          transform: translateX(50px);
        }
        .caracteristicas-caracteristica:nth-child(2n)
          .caracteristicas-caracteristica-imagen {
          grid-column: 2;
        }
        .caracteristicas-caracteristica:nth-child(2n)
          .caracteristicas-caracteristica-imagen.animado:not(.is-visible) {
          transform: translateX(50px);
        }
        .caracteristicas-caracteristica:nth-child(2n)
          .caracteristicas-caracteristica-texto {
          grid-column: 1;
          grid-row: 2;
        }
      }
      .personas > .inner {
        display: grid;
        grid-column-gap: 1.375rem;
        grid-row-gap: 5rem;
      }
      .personas-persona-imagen img {
        display: block;
        width: 100%;
      }
      .personas-persona-nombre {
        color: #34779b;
        margin: 0.75em 0 0.375em;
      }
      @media only screen and (min-width: 400px) {
        .personas > .inner {
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }
      }
      .cabecera > .inner,
      .cabeceraPrincipal > .inner,
      .caracteristicas > .inner,
      .contacto > .inner,
      .empresas-lista,
      .personas > .inner,
      .piePrincipal > .inner {
        width: Min(80rem, 90vw);
        margin-left: auto;
        margin-right: auto;
      }
      .empresa-contenido,
      .empresas-filtro,
      .texto.texto--ancho-completo > .inner,
      .texto.texto--ancho-estrecho.texto--alineacion-izquierda > .inner,
      .titulo.titulo--ancho-completo > .inner,
      .titulo.titulo--ancho-estrecho.titulo--alineacion-izquierda > .inner {
        width: Min(64.375rem, 90vw);
        margin-left: auto;
        margin-right: auto;
      }
      .texto.texto--ancho-estrecho.texto--alineacion-centrado > .inner,
      .titulo.titulo--ancho-estrecho.titulo--alineacion-centrado > .inner {
        width: Min(50.625rem, 90vw);
        margin-left: auto;
        margin-right: auto;
      }
      .cabecera-titulo > h1 {
        font-size: Max(2.5rem, Min(8.85vw, 4.25rem));
        font-weight: 400;
        line-height: 1;
      }
      .contacto-titulo,
      .empresa-contenido-titulo,
      .texto-titulo,
      .titulo-titulo {
        font-size: Max(1.5625rem, Min(4.95vw, 2.375rem));
        font-weight: 400;
        line-height: 1;
      }
      .caracteristicas-caracteristica-titulo,
      .formulario-titulo,
      .personas-persona-nombre {
        font-size: Max(1.4375rem, Min(3.78vw, 1.8125rem));
        font-weight: 400;
        line-height: 1;
      }
      .personas-persona-cargo {
        font-size: 1.1875rem;
        font-weight: 400;
        line-height: 1;
      }
      .cabecera-boton > a,
      .contacto-formulario button,
      .empresa-contenido-enlaces a,
      .empresas-filtro-campos button {
        text-transform: uppercase;
        font-size: 0.875rem;
        font-weight: 600;
        padding: 0.5em 2em 0.5em 1em;
        text-decoration: none;
        transition: 0.3s;
        display: inline-flex;
        align-items: center;
        border: 0;
        border-radius: 0.1875rem;
      }
      .cabecera-boton > a:after,
      .contacto-formulario button:after,
      .empresa-contenido-enlaces a:after,
      .empresas-filtro-campos button:after {
        content: "";
        width: 11px;
        height: 7px;
        margin-left: 1em;
      }
      .cabecera-boton > a,
      .empresas-filtro-campos button {
        background: #fff;
        color: #34779b;
      }
      .cabecera-boton > a:after,
      .empresas-filtro-campos button:after {
        background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='11' height='7'%3E%3Cpath d='M10.246.668a1 1 0 00-1.41 0l-3.59 3.54-3.54-3.54a1 1 0 10-1.41 1.42l4.24 4.24a1 1 0 001.42 0l4.29-4.24a1 1 0 000-1.42z' fill='%233B87AF'/%3E%3C/svg%3E");
        flex: 1;
      }
      .cabecera-boton > a:focus,
      .cabecera-boton > a:hover,
      .empresas-filtro-campos button:focus,
      .empresas-filtro-campos button:hover {
        background: #34779b;
        color: #fff;
      }
      .cabecera-boton > a:focus:after,
      .cabecera-boton > a:hover:after,
      .empresas-filtro-campos button:focus:after,
      .empresas-filtro-campos button:hover:after {
        background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='11' height='7'%3E%3Cpath d='M10.246.668a1 1 0 00-1.41 0l-3.59 3.54-3.54-3.54a1 1 0 10-1.41 1.42l4.24 4.24a1 1 0 001.42 0l4.29-4.24a1 1 0 000-1.42z' fill='%23fff'/%3E%3C/svg%3E");
      }
      .contacto-formulario button,
      .empresa-contenido-enlaces a {
        background: #34779b;
        color: #fff;
        border: 1px solid #34779b;
      }
      .contacto-formulario button:after,
      .empresa-contenido-enlaces a:after {
        background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='11' height='7'%3E%3Cpath d='M10.246.668a1 1 0 00-1.41 0l-3.59 3.54-3.54-3.54a1 1 0 10-1.41 1.42l4.24 4.24a1 1 0 001.42 0l4.29-4.24a1 1 0 000-1.42z' fill='%23fff'/%3E%3C/svg%3E");
      }
      .contacto-formulario button:focus,
      .contacto-formulario button:hover,
      .empresa-contenido-enlaces a:focus,
      .empresa-contenido-enlaces a:hover {
        background: #fff;
        color: #34779b;
      }
      .contacto-formulario button:focus:after,
      .contacto-formulario button:hover:after,
      .empresa-contenido-enlaces a:focus:after,
      .empresa-contenido-enlaces a:hover:after {
        background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='11' height='7'%3E%3Cpath d='M10.246.668a1 1 0 00-1.41 0l-3.59 3.54-3.54-3.54a1 1 0 10-1.41 1.42l4.24 4.24a1 1 0 001.42 0l4.29-4.24a1 1 0 000-1.42z' fill='%233B87AF'/%3E%3C/svg%3E");
      }
      .titulo {
        overflow: hidden;
      }
      .titulo.titulo--ancho-estrecho.titulo--alineacion-izquierda
        > .inner
        > .titulo-contenido {
        max-width: Min(62.5rem, 90vw);
      }
      .titulo-titulo {
        color: #34779b;
        margin: 0 0 1em;
        max-width: 20.5em;
      }
      .animado {
        transition: opacity 1s, transform 1s;
      }
      .animado:not(.is-visible) {
        transform: translateY(50px);
        opacity: 0;
      }
    </style>
    <style type="text/css">
      .recentcomments a {
        display: inline !important;
        padding: 0 !important;
        margin: 0 !important;
      }
    </style>
    <link
      rel="icon"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/Captura-de-pantalla-2021-06-11-a-las-13.54.41-150x150.png"
      sizes="32x32"
    />
    <link
      rel="icon"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/Captura-de-pantalla-2021-06-11-a-las-13.54.41-200x200.png"
      sizes="192x192"
    />
    <link
      rel="apple-touch-icon"
      href="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/Captura-de-pantalla-2021-06-11-a-las-13.54.41-200x200.png"
    />
    <meta
      name="msapplication-TileImage"
      content="https://cavig.com/wp-content/uploads/2021/06/Captura-de-pantalla-2021-06-11-a-las-13.54.41.png"
    />
  </head>
  <body
    class="page-template page-template-bloques page-template-bloques-php page page-id-140 preload primer-bloque-cabecera con-cabecera con-titulo con-texto con-contacto"
  >
    <header class="cabeceraPrincipal">
      <div class="inner">
        <div class="cabeceraPrincipal-logo">
          <a
            href="https://web.archive.org/web/20250418055804/https://cavig.com/"
          >
            <picture>
              <source
                srcset="
                  https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/img/logo.png.webp
                "
                type="image/webp"
              />
              <img
                src="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/img/logo.png"
                alt="CAV Investment Group"
                width="191"
                height="43"
              />
            </picture>
          </a>
        </div>

        <div class="cabeceraPrincipal-menu">
          <ul id="menu-principal" class="menu">
            <li
              id="menu-item-143"
              class="menu-item menu-item-type-post_type menu-item-object-page current-menu-item page_item page-item-140 current_page_item menu-item-143"
            >
              <a href="about-us.html" aria-current="page">About us</a>
            </li>
            <li
              id="menu-item-159"
              class="menu-item menu-item-type-post_type menu-item-object-page menu-item-159"
            >
              <a href="how-we-work.html">How We Work</a>
            </li>
            <li
              id="menu-item-104"
              class="menu-item menu-item-type-post_type menu-item-object-page menu-item-104"
            >
              <a href="portfolio.html">Portfolio</a>
            </li>
            <li
              id="menu-item-282"
              class="menu-item menu-item-type-post_type menu-item-object-page menu-item-282"
            >
              <a href="exits.html">Exits</a>
            </li>
            <li
              id="menu-item-112"
              class="menu-item menu-item-type-post_type menu-item-object-page menu-item-112"
            >
              <a href="contact.html">Contact</a>
            </li>
          </ul>
        </div>
      </div>
    </header>
    <div class="contenedor">
      <div
        class="bloque cabecera bloque--cabecera impar siguiente-titulo"
        style=""
      >
        <div class="inner">
          <div class="cabecera-titulo">
            <h1>About us</h1>
          </div>
        </div>
      </div>
      <div
        class="bloque titulo bloque--titulo impar siguiente-texto anterior-cabecera titulo--ancho-estrecho titulo--alineacion-izquierda"
        style=""
      >
        <div class="inner">
          <div class="titulo-contenido">
            <h2 class="titulo-titulo animado">
              Unique Experience in the Real Estate Industry
            </h2>
          </div>
        </div>
      </div>
      <div
        class="bloque texto bloque--texto impar siguiente-contacto anterior-titulo texto--con-imagen texto--ancho-estrecho texto--alineacion-centrado"
        style=""
      >
        <div class="inner">
          <div class="texto-contenido">
            <div class="texto-imagen animado">
              <picture
                ><source
                  srcset="
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-400x600.jpeg.webp    400w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-200x300.jpeg.webp    200w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-682x1024.jpeg.webp   682w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-768x1153.jpeg.webp   768w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-1023x1536.jpeg.webp 1023w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-133x200.jpeg.webp    133w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12.jpeg.webp           1066w
                  "
                  type="image/webp"
                  sizes="(max-width: 400px) 100vw, 400px" />
                <img
                  width="400"
                  height="600"
                  src="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-400x600.jpeg"
                  class="attachment-texto size-texto"
                  alt=""
                  decoding="async"
                  fetchpriority="high"
                  srcset="
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-400x600.jpeg    400w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-200x300.jpeg    200w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-682x1024.jpeg   682w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-768x1153.jpeg   768w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-1023x1536.jpeg 1023w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-133x200.jpeg    133w,
                    https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12.jpeg           1066w
                  "
                  sizes="(max-width: 400px) 100vw, 400px"
              /></picture>
            </div>

            <div class="texto-texto animado">
              <p>
                CAV Investment Group was founded by Simon Baker, Serial Investor
                in and Advisor to the Global PropTech Industry.
              </p>
              <p>
                Former CEO and Managing Director of the REA Group, during his
                eight years leadership, REA Group’s revenues increased from
                AUD$4m to AUD$155m and an AUD$6m loss was turned into an EBITDA
                profit of AUD$30m with the market cap increasing from AUD$8m to
                a high of AUD$1 billion.
              </p>
              <p>
                With <strong>more than 30 years</strong> of experience in the
                classifieds and marketplaces industry, Simon
                <strong
                  >has also founded more than 10 different companies</strong
                >
                and has been the Chairman of several ASX listed companies such
                as iProperty Group, CarAdvice and Mitula Group amongst others.
              </p>
              <p>
                Today Simon is the Chairman of the ASX listed Proptech Group
                Ltd. (ASX:TPG).
              </p>
              <p>
                Contact us to find unparallel expertise in Property Classifieds,
                Online Brokers, Home Search, iBuyers and Marketplaces in
                general. We are looking for top-notch Founders of
                Next-generation companies in the Global Real Estate and Proptech
                arena.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        class="bloque contacto bloque--contacto impar anterior-texto"
        style=""
        id="contact"
      >
        <div class="inner">
          <h2 class="contacto-titulo animado">Contact Us</h2>

          <div class="contacto-formulario animado">
            <form
              action="https://web.archive.org/web/20250418055804/https://cavig.com/about-us/"
              method="post"
              enctype="multipart/form-data"
              class="animado"
              id="contacto"
            >
              <div class="contacto-formulario-wrapper">
                <input type="hidden" name="formulario" value="contacto" /><input
                  type="text"
                  class="miel"
                  name="contacto[contacto]"
                  placeholder="No me rellenes si me ves"
                />
                <p class="form-row campo campo--input campo--50 campo--nombre">
                  <label for="contacto-nombre">First Name&nbsp;* </label>
                  <input
                    type="input"
                    id="contacto-nombre"
                    name="contacto[nombre]"
                    placeholder=""
                    value=""
                    required
                  />
                </p>
                <p
                  class="form-row campo campo--input campo--50 campo--apellidos"
                >
                  <label for="contacto-apellidos">Last Name&nbsp;* </label>
                  <input
                    type="input"
                    id="contacto-apellidos"
                    name="contacto[apellidos]"
                    placeholder=""
                    value=""
                    required
                  />
                </p>
                <p class="form-row campo campo--input campo--50 campo--email">
                  <label for="contacto-email">Email&nbsp;* </label>
                  <input
                    type="input"
                    id="contacto-email"
                    name="contacto[email]"
                    placeholder=""
                    value=""
                    required
                  />
                </p>
                <p class="form-row campo campo--file campo--50 campo--adjunto">
                  <label for="contacto-adjunto">Drop file or browse </label>
                  <input
                    type="file"
                    id="contacto-adjunto"
                    name="contacto[adjunto]"
                    placeholder=""
                    value=""
                  />
                </p>
                <p
                  class="form-row campo campo--textarea campo--100 campo--mensaje"
                >
                  <label for="contacto-mensaje">Message&nbsp;* </label>
                  <textarea
                    type="textarea"
                    id="contacto-mensaje"
                    name="contacto[mensaje]"
                    placeholder=""
                    required
                  ></textarea>
                </p>
                <p
                  class="form-row campo campo--submit campo--100 campo--acepto"
                >
                  <button type="submit" class="principal">Contact us</button>
                </p>
              </div>
            </form>
          </div>

          <div class="contacto-texto animado">
            <p>
              At CAV Investment Group we are continually looking for new and
              exciting ways to invest in the future of the Digital Classifieds
              industry. Please feel free to contact us if you have any enquires.
            </p>
            <p>Remember to fill all the fields with *</p>
          </div>
        </div>
      </div>
    </div>
    <footer class="piePrincipal sin-telefono" style="">
      <div class="inner">
        <div class="piePrincipal-logo">
          <a
            href="https://web.archive.org/web/20250418055804/https://cavig.com/"
          >
            <picture>
              <source
                srcset="
                  https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/img/logo.png.webp
                "
                type="image/webp"
              />
              <img
                src="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/img/logo.png"
                alt="CAV Investment Group"
                width="191"
                height="43"
              />
            </picture>
          </a>
        </div>

        <div class="piePrincipal-copyright">© All rights reserved</div>

        <div class="piePrincipal-contacto">
          <div class="piePrincipal-contacto-direccion"></div>

          <div class="piePrincipal-contacto-redes">
            <div class="redes">
              <a
                class="red"
                href="https://web.archive.org/web/20250418055804/https://www.linkedin.com/company/cav-investment-group/about/"
                title="LinkedIn"
                target="_blank"
                rel="noopener"
                ><svg width="20" height="20" class="icon icon-LinkedIn">
                  <use
                    xlink:href="/web/20250418055804im_/https://cavig.com/about-us/#LinkedIn"
                  /></svg
              ></a>
            </div>
          </div>
        </div>
      </div>
    </footer>
    <script>
      addEventListener("load", function () {
        document.body.classList.remove("preload");
      });

      if (sessionStorage.raton === "true") {
        document.body.classList.add("con-raton");
      }

      if (sessionStorage.tactil === "true") {
        document.body.classList.add("con-tactil");
      }

      addEventListener("mousemove", compruebaRaton);
      var ultimaLectura = null;

      function compruebaRaton() {
        if (ultimaLectura === null) {
          ultimaLectura = new Date().getTime();
          return;
        }

        if (new Date().getTime() - ultimaLectura < 50) {
          sessionStorage.raton = "true";
          document.body.classList.add("con-raton");
          removeEventListener("mousemove", compruebaRaton);
        } else {
          ultimaLectura = new Date().getTime();
        }
      }

      addEventListener(
        "keyup",
        function (e) {
          if (e.key === "Tab") {
            document.body.classList.remove("con-raton");
            addEventListener("mousemove", compruebaRaton);
          }
        },
        true
      );
    </script>
    <div
      style="
        width: 0;
        height: 0;
        position: absolute;
        visibility: hidden;
        overflow: hidden;
      "
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <symbol id="Facebook" viewbox="0 0 15 22">
          <path
            d="M13.83 8.44A1 1 0 0013 8h-3V6h2.2a1 1 0 001-1V1a1 1 0 00-1-1H9a5.8 5.8 0 00-5.8 5.8V8H1a1 1 0 00-1 1v4a1 1 0 001 1h2.2v7a1 1 0 001 1H9a1 1 0 001-1v-7h1.4a1 1 0 00.93-.63l1.6-4a1 1 0 00-.1-.93zM10.72 12H9a1 1 0 00-1 1v7H5.2v-7a1 1 0 00-1-1H2v-2h2.2a1 1 0 001-1V5.8A3.81 3.81 0 019 2h2.2v2h-.8a2.5 2.5 0 00-1.92.52A1.74 1.74 0 008 5.8V9a1 1 0 001 1h2.52l-.8 2z"
            fill="currentColor"
          />
        </symbol>
        <symbol id="Instagram" viewbox="0 0 20 20">
          <path
            d="M10 5a5 5 0 100 10 5 5 0 000-10zm0 8a3 3 0 110-6 3 3 0 010 6zm5-9a1 1 0 100 2 1 1 0 000-2zm4.94 2.24a7.51 7.51 0 00-.48-2.5 5.56 5.56 0 00-3.2-3.2 7.51 7.51 0 00-2.5-.48C12.79 0 12.47 0 10 0 7.53 0 7.21 0 6.24.06a7.51 7.51 0 00-2.5.48 5.56 5.56 0 00-3.2 3.2 7.51 7.51 0 00-.48 2.5C0 7.22 0 7.54 0 10s0 2.78.06 3.76c.017.855.179 1.7.48 2.5a5.56 5.56 0 003.2 3.2 7.51 7.51 0 002.5.48c1 0 1.29.06 3.76.06 2.47 0 2.79 0 3.76-.06a7.51 7.51 0 002.5-.48 5.56 5.56 0 003.2-3.2 7.51 7.51 0 00.48-2.5c0-1 .06-1.3.06-3.76s0-2.78-.06-3.76zm-2 7.43a6 6 0 01-.35 1.86 3.4 3.4 0 01-.82 1.25 3.26 3.26 0 01-1.25.81 5.75 5.75 0 01-1.87.36c-.94 0-1.23.05-3.66.05H6.34a6 6 0 01-1.87-.35 3.4 3.4 0 01-1.25-.82 3.26 3.26 0 01-.81-1.25 5.74 5.74 0 01-.36-1.86v-3.67-3.67a5.86 5.86 0 01.36-1.87 3.3 3.3 0 01.81-1.24 3.26 3.26 0 011.25-.81 5.75 5.75 0 011.87-.36C7.28 2 7.57 2 10 2h3.66a5.87 5.87 0 011.88.36 3.3 3.3 0 011.24.81c.363.349.64.776.81 1.25A5.74 5.74 0 0118 6.33c0 1 .05 1.25.05 3.67S18 12.71 18 13.67h-.06z"
            fill="currentColor"
          />
        </symbol>
        <symbol id="LinkedIn" viewbox="0 0 24 24">
          <path
            fill="currentColor"
            d="M19 0H5C2.24 0 0 2.24 0 5v14c0 2.76 2.24 5 5 5h14c2.76 0 5-2.24 5-5V5c0-2.76-2.24-5-5-5zM8 19H5V8h3v11zM6.5 6.73c-.97 0-1.75-.8-1.75-1.76S5.53 3.2 6.5 3.2s1.75.8 1.75 1.77-.78 1.76-1.75 1.76zM20 19h-3v-5.6c0-3.37-4-3.12-4 0V19h-3V8h3v1.76c1.4-2.58 7-2.77 7 2.48V19z"
          />
        </symbol>
        <symbol id="Twitter" viewbox="0 0 23 19">
          <path
            d="M22 1.309a1 1 0 00-1.51-.86 7.48 7.48 0 01-1.873.793A5.153 5.153 0 0015.241 0a5.232 5.232 0 00-5.223 5.064A11.032 11.032 0 013.205 1.14a1.012 1.012 0 00-.857-.366.999.999 0 00-.785.5 5.276 5.276 0 00-.242 4.769l-.002.002a1.041 1.041 0 00-.496.889c-.001.147.007.293.027.439a5.185 5.185 0 001.568 3.312.999.999 0 00-.066.77 5.204 5.204 0 002.362 2.922 7.465 7.465 0 01-3.59.448 1 1 0 00-.665 1.833 12.942 12.942 0 007.01 2.062 12.788 12.788 0 0012.465-9.364c.353-1.183.533-2.411.535-3.646l-.001-.2A5.77 5.77 0 0022 1.309zM18.694 4.52a.995.995 0 00-.234.702c.01.165.009.33.009.487a10.826 10.826 0 01-.454 3.081A10.684 10.684 0 017.469 16.72c-.86 0-1.715-.101-2.55-.301a9.48 9.48 0 002.942-1.564 1.001 1.001 0 00-.602-1.786 3.208 3.208 0 01-2.214-.934c.15-.028.298-.064.445-.106a1 1 0 00-.08-1.943A3.198 3.198 0 013.16 8.36c.181.025.363.04.545.046a1.02 1.02 0 00.984-.696 1 1 0 00-.4-1.137 3.195 3.195 0 01-1.419-2.87 13.014 13.014 0 008.21 3.479 1.02 1.02 0 00.817-.359 1 1 0 00.206-.868 3.158 3.158 0 01-.087-.728 3.23 3.23 0 014.506-2.962c.403.175.766.433 1.065.756a.993.993 0 00.921.297c.41-.08.816-.187 1.212-.322a6.682 6.682 0 01-1.026 1.525z"
            fill="currentColor"
          />
        </symbol>
      </svg>
    </div>
    <script
      type="text/javascript"
      src="https://web.archive.org/web/20250418055804js_/https://cavig.com/wp-content/themes/cavig/dist/app.0cd459ca.js?ver=6.7.2"
      id="dist/app.0cd459ca.js-js"
    ></script>
  </body>
</html>
