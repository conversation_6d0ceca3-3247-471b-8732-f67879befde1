<html></html>
<head><title>About Us</title></head>
<body class="page-template page-template-bloques page-template-bloques-php page page-id-140 primer-bloque-cabecera con-cabecera con-titulo con-texto con-contacto con-raton"><!-- BEGIN WAYBACK TOOLBAR INSERT -->
<script>__wm.rw(0);</script>
<div id="wm-ipp-base" lang="en" style="display: block; direction: ltr; height: 67px;" toolbar-mode="auto">
</div><div id="wm-ipp-print">The Wayback Machine - https://web.archive.org/web/20250418055804/https://cavig.com/about-us/</div>
<script type="text/javascript">//<![CDATA[
__wm.bt(750,27,25,2,"web","https://cavig.com/about-us/","20250418055804",1996,"https://web-static.archive.org/_static/",["https://web-static.archive.org/_static/css/banner-styles.css?v=p7PEIJWi","https://web-static.archive.org/_static/css/iconochive.css?v=3PDvdIFv"], false);
  __wm.rw(1);
//]]></script>
<!-- END WAYBACK TOOLBAR INSERT -->
 
<header class="cabeceraPrincipal">
    <div class="inner">
        <div class="cabeceraPrincipal-logo">
            <a href="https://web.archive.org/web/20250418055804/https://cavig.com/">
                <picture>
                    <source srcset="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/img/logo.png.webp" type="image/webp">
                    <img src="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/img/logo.png" alt="CAV Investment Group" width="191" height="43">
                </picture>
            </a>
        </div>

        <div class="cabeceraPrincipal-menu">
            <ul id="menu-principal" class="menu"><li id="menu-item-143" class="menu-item menu-item-type-post_type menu-item-object-page current-menu-item page_item page-item-140 current_page_item menu-item-143"><a href="https://web.archive.org/web/20250418055804/https://cavig.com/about-us/" aria-current="page">About us</a></li>
<li id="menu-item-159" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-159"><a href="https://web.archive.org/web/20250418055804/https://cavig.com/how-we-work/">How We Work</a></li>
<li id="menu-item-104" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-104"><a href="https://web.archive.org/web/20250418055804/https://cavig.com/our-portfolio/">Portfolio</a></li>
<li id="menu-item-282" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-282"><a href="https://web.archive.org/web/20250418055804/https://cavig.com/exits/">Exits</a></li>
<li id="menu-item-112" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-112"><a href="https://web.archive.org/web/20250418055804/https://cavig.com/contact-us/">Contact</a></li>
</ul>        </div>
    </div>
</header>
<div class="contenedor">
<div class="bloque cabecera bloque--cabecera impar siguiente-titulo" style="">
	
    <div class="inner">
        <div class="cabecera-titulo">
			<h1>About us</h1>
        </div>

		
		    </div>
</div><div class="bloque titulo bloque--titulo impar siguiente-texto anterior-cabecera titulo--ancho-estrecho titulo--alineacion-izquierda" style="">
    <div class="inner">
        <div class="titulo-contenido">
            <h2 class="titulo-titulo animado">
				Unique Experience in the Real Estate Industry            </h2>
        </div>
    </div>
</div><div class="bloque texto bloque--texto impar siguiente-contacto anterior-titulo texto--con-imagen texto--ancho-estrecho texto--alineacion-centrado" style="">
    <div class="inner">
        <div class="texto-contenido">
			
			                <div class="texto-imagen animado">
					<picture><source srcset="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-400x600.jpeg.webp 400w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-200x300.jpeg.webp 200w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-682x1024.jpeg.webp 682w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-768x1153.jpeg.webp 768w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-1023x1536.jpeg.webp 1023w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-133x200.jpeg.webp 133w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12.jpeg.webp 1066w" type="image/webp" sizes="(max-width: 400px) 100vw, 400px"><img width="400" height="600" src="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-400x600.jpeg" class="attachment-texto size-texto" alt="" decoding="async" fetchpriority="high" srcset="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-400x600.jpeg 400w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-200x300.jpeg 200w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-682x1024.jpeg 682w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-768x1153.jpeg 768w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-1023x1536.jpeg 1023w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12-133x200.jpeg 133w, https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/uploads/2021/06/WhatsApp-Image-2021-06-11-at-15.26.12.jpeg 1066w" sizes="(max-width: 400px) 100vw, 400px"></picture>                </div>
			
            <div class="texto-texto animado">
				<p>CAV Investment Group was founded by Simon Baker, Serial Investor in and Advisor to the Global PropTech Industry.</p>
<p>Former CEO and Managing Director of the REA Group, during his eight years leadership, REA Group’s revenues increased from AUD$4m to AUD$155m and an AUD$6m loss was turned into an EBITDA profit of AUD$30m with the market cap increasing from AUD$8m to a high of AUD$1 billion.</p>
<p>With <strong>more than 30 years</strong> of experience in the classifieds and marketplaces industry, Simon <strong>has also founded more than 10 different companies</strong> and has been the Chairman of several ASX listed companies such as iProperty Group, CarAdvice and Mitula Group amongst others.</p>
<p>Today Simon is the Chairman of the ASX listed Proptech Group Ltd. (ASX:TPG).</p>
<p>Contact us to find unparallel expertise in Property Classifieds, Online Brokers, Home Search, iBuyers and Marketplaces in general. We are looking for top-notch Founders of Next-generation companies in the Global Real Estate and Proptech arena.</p>
            </div>

			        </div>
    </div>
</div><div class="bloque contacto bloque--contacto impar anterior-texto" style="" id="contact">
    <div class="inner">
		            <h2 class="contacto-titulo animado">
				Contact Us            </h2>
		
        <div class="contacto-formulario animado">
			
            <form action="https://web.archive.org/web/20250418055804/https://cavig.com/about-us/" method="post" enctype="multipart/form-data" class="animado inicializado" id="contacto">
				                    <div class="contacto-formulario-wrapper"><input type="hidden" name="formulario" value="contacto"><input type="text" class="miel" name="contacto[contacto]" placeholder="No me rellenes si me ves">            <p class="form-row campo campo--input campo--50 campo--nombre">        <label for="contacto-nombre">First Name&nbsp;*        </label>        <input type="input" id="contacto-nombre" name="contacto[nombre]" placeholder="" value="" required="">            <label for="contacto-nombre" class="is-inactiva">First Name&nbsp;*        </label></p>            <p class="form-row campo campo--input campo--50 campo--apellidos">        <label for="contacto-apellidos">Last Name&nbsp;*        </label>        <input type="input" id="contacto-apellidos" name="contacto[apellidos]" placeholder="" value="" required="">            <label for="contacto-apellidos" class="is-inactiva">Last Name&nbsp;*        </label></p>            <p class="form-row campo campo--input campo--50 campo--email">        <label for="contacto-email">Email&nbsp;*        </label>        <input type="input" id="contacto-email" name="contacto[email]" placeholder="" value="" required="">            <label for="contacto-email" class="is-inactiva">Email&nbsp;*        </label></p>            <p class="form-row campo campo--file campo--50 campo--adjunto">        <label for="contacto-adjunto">Drop file or browse        </label>        <input type="file" id="contacto-adjunto" name="contacto[adjunto]" placeholder="" value="">            <label for="contacto-adjunto" class="is-inactiva">Drop file or browse        </label><div>&nbsp;</div></p>            <p class="form-row campo campo--textarea campo--100 campo--mensaje">        <label for="contacto-mensaje">Message&nbsp;*        </label>        <textarea type="textarea" id="contacto-mensaje" name="contacto[mensaje]" placeholder="" required=""></textarea>            <label for="contacto-mensaje" class="is-inactiva">Message&nbsp;*        </label></p>            <p class="form-row campo campo--submit campo--100 campo--acepto">        <button type="submit" class="principal">Contact us        </button>            </p>                    </div>
				            </form>
        </div>

		            <div class="contacto-texto animado">
				<p>At CAV Investment Group we are continually looking for new and exciting ways to invest in the future of the Digital Classifieds industry. Please feel free to contact us if you have any enquires.</p>
<p>Remember to fill all the fields with *</p>
            </div>
		    </div>
</div></div>
<footer class="piePrincipal sin-telefono" style="">
    <div class="inner">
        <div class="piePrincipal-logo">
            <a href="https://web.archive.org/web/20250418055804/https://cavig.com/">
                <picture>
                    <source srcset="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/img/logo.png.webp" type="image/webp">
                    <img src="https://web.archive.org/web/20250418055804im_/https://cavig.com/wp-content/themes/cavig/img/logo.png" alt="CAV Investment Group" width="191" height="43">
                </picture>
            </a>
        </div>

        <div class="piePrincipal-copyright">
            © All rights reserved
        </div>

        <div class="piePrincipal-contacto">
            <div class="piePrincipal-contacto-direccion">
				            </div>

			
            <div class="piePrincipal-contacto-redes">
				<div class="redes"><a class="red" href="https://web.archive.org/web/20250418055804/https://www.linkedin.com/company/cav-investment-group/about/" title="LinkedIn" target="_blank" rel="noopener"><svg width="20" height="20" class="icon icon-LinkedIn"><use xlink:href="/web/20250418055804im_/https://cavig.com/about-us/#LinkedIn"></use></svg></a></div>            </div>
        </div>
    </div>
</footer>
<script>
  addEventListener('load', function() {
    document.body.classList.remove('preload');
  });

  if (sessionStorage.raton === 'true') {
    document.body.classList.add('con-raton');
  }

  if (sessionStorage.tactil === 'true') {
    document.body.classList.add('con-tactil');
  }

  addEventListener('mousemove', compruebaRaton);
  var ultimaLectura = null;

  function compruebaRaton() {
    if (ultimaLectura === null) {
      ultimaLectura = new Date().getTime();
      return;
    }

    if (new Date().getTime() - ultimaLectura < 50) {
      sessionStorage.raton = 'true';
      document.body.classList.add('con-raton');
      removeEventListener('mousemove', compruebaRaton);
    } else {
      ultimaLectura = new Date().getTime();
    }
  }

  addEventListener('keyup', function(e) {
    if (e.key === 'Tab') {
      document.body.classList.remove('con-raton');
      addEventListener('mousemove', compruebaRaton);
    }
  }, true);
</script>
<div style="width:0;height:0;position:absolute;visibility:hidden;overflow:hidden"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><symbol id="Facebook" viewBox="0 0 15 22"><path d="M13.83 8.44A1 1 0 0013 8h-3V6h2.2a1 1 0 001-1V1a1 1 0 00-1-1H9a5.8 5.8 0 00-5.8 5.8V8H1a1 1 0 00-1 1v4a1 1 0 001 1h2.2v7a1 1 0 001 1H9a1 1 0 001-1v-7h1.4a1 1 0 00.93-.63l1.6-4a1 1 0 00-.1-.93zM10.72 12H9a1 1 0 00-1 1v7H5.2v-7a1 1 0 00-1-1H2v-2h2.2a1 1 0 001-1V5.8A3.81 3.81 0 019 2h2.2v2h-.8a2.5 2.5 0 00-1.92.52A1.74 1.74 0 008 5.8V9a1 1 0 001 1h2.52l-.8 2z" fill="currentColor"></path></symbol><symbol id="Instagram" viewBox="0 0 20 20"><path d="M10 5a5 5 0 100 10 5 5 0 000-10zm0 8a3 3 0 110-6 3 3 0 010 6zm5-9a1 1 0 100 2 1 1 0 000-2zm4.94 2.24a7.51 7.51 0 00-.48-2.5 5.56 5.56 0 00-3.2-3.2 7.51 7.51 0 00-2.5-.48C12.79 0 12.47 0 10 0 7.53 0 7.21 0 6.24.06a7.51 7.51 0 00-2.5.48 5.56 5.56 0 00-3.2 3.2 7.51 7.51 0 00-.48 2.5C0 7.22 0 7.54 0 10s0 2.78.06 3.76c.017.855.179 1.7.48 2.5a5.56 5.56 0 003.2 3.2 7.51 7.51 0 002.5.48c1 0 1.29.06 3.76.06 2.47 0 2.79 0 3.76-.06a7.51 7.51 0 002.5-.48 5.56 5.56 0 003.2-3.2 7.51 7.51 0 00.48-2.5c0-1 .06-1.3.06-3.76s0-2.78-.06-3.76zm-2 7.43a6 6 0 01-.35 1.86 3.4 3.4 0 01-.82 1.25 3.26 3.26 0 01-1.25.81 5.75 5.75 0 01-1.87.36c-.94 0-1.23.05-3.66.05H6.34a6 6 0 01-1.87-.35 3.4 3.4 0 01-1.25-.82 3.26 3.26 0 01-.81-1.25 5.74 5.74 0 01-.36-1.86v-3.67-3.67a5.86 5.86 0 01.36-1.87 3.3 3.3 0 01.81-1.24 3.26 3.26 0 011.25-.81 5.75 5.75 0 011.87-.36C7.28 2 7.57 2 10 2h3.66a5.87 5.87 0 011.88.36 3.3 3.3 0 011.24.81c.363.349.64.776.81 1.25A5.74 5.74 0 0118 6.33c0 1 .05 1.25.05 3.67S18 12.71 18 13.67h-.06z" fill="currentColor"></path></symbol><symbol id="LinkedIn" viewBox="0 0 24 24"><path fill="currentColor" d="M19 0H5C2.24 0 0 2.24 0 5v14c0 2.76 2.24 5 5 5h14c2.76 0 5-2.24 5-5V5c0-2.76-2.24-5-5-5zM8 19H5V8h3v11zM6.5 6.73c-.97 0-1.75-.8-1.75-1.76S5.53 3.2 6.5 3.2s1.75.8 1.75 1.77-.78 1.76-1.75 1.76zM20 19h-3v-5.6c0-3.37-4-3.12-4 0V19h-3V8h3v1.76c1.4-2.58 7-2.77 7 2.48V19z"></path></symbol><symbol id="Twitter" viewBox="0 0 23 19"><path d="M22 1.309a1 1 0 00-1.51-.86 7.48 7.48 0 01-1.873.793A5.153 5.153 0 0015.241 0a5.232 5.232 0 00-5.223 5.064A11.032 11.032 0 013.205 1.14a1.012 1.012 0 00-.857-.366.999.999 0 00-.785.5 5.276 5.276 0 00-.242 4.769l-.002.002a1.041 1.041 0 00-.496.889c-.001.147.007.293.027.439a5.185 5.185 0 001.568 3.312.999.999 0 00-.066.77 5.204 5.204 0 002.362 2.922 7.465 7.465 0 01-3.59.448 1 1 0 00-.665 1.833 12.942 12.942 0 007.01 2.062 12.788 12.788 0 0012.465-9.364c.353-1.183.533-2.411.535-3.646l-.001-.2A5.77 5.77 0 0022 1.309zM18.694 4.52a.995.995 0 00-.234.702c.01.165.009.33.009.487a10.826 10.826 0 01-.454 3.081A10.684 10.684 0 017.469 16.72c-.86 0-1.715-.101-2.55-.301a9.48 9.48 0 002.942-1.564 1.001 1.001 0 00-.602-1.786 3.208 3.208 0 01-2.214-.934c.15-.028.298-.064.445-.106a1 1 0 00-.08-1.943A3.198 3.198 0 013.16 8.36c.181.025.363.04.545.046a1.02 1.02 0 00.984-.696 1 1 0 00-.4-1.137 3.195 3.195 0 01-1.419-2.87 13.014 13.014 0 008.21 3.479 1.02 1.02 0 00.817-.359 1 1 0 00.206-.868 3.158 3.158 0 01-.087-.728 3.23 3.23 0 014.506-2.962c.403.175.766.433 1.065.756a.993.993 0 00.921.297c.41-.08.816-.187 1.212-.322a6.682 6.682 0 01-1.026 1.525z" fill="currentColor"></path></symbol></svg></div><script type="text/javascript" src="https://web.archive.org/web/20250418055804js_/https://cavig.com/wp-content/themes/cavig/dist/app.0cd459ca.js?ver=6.7.2" id="dist/app.0cd459ca.js-js"></script>



</body>
</html>
